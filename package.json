{"name": "notion_blog", "version": "1.0.0", "main": "index.js", "scripts": {"docs:dev": "vitepress dev docs", "docs:build": "npm run sync && vitepress build docs", "serve": "vitepress serve docs", "sync": "node scripts/sync-notion.js", "watch": "node scripts/watch-notion.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"node-fetch": "2.7.0", "vitepress": "^1.6.4"}, "dependencies": {"@notionhq/client": "^4.0.1", "dotenv": "^17.2.1", "form-data": "^4.0.4", "gray-matter": "^4.0.3"}}