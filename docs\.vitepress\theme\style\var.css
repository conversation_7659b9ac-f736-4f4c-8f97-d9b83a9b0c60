/* var.css */



:root {

    --vp-c-brand-1: rgb(65,184,130);
    --vp-c-brand-2: rgba(65,220,130,.5);
    --vp-c-brand-3: rgba(90, 41, 228, .7);
}

.dark {

    --vp-c-brand-1: rgb(65,184,130);
    --vp-c-brand-2:  rgba(65,220,130,.5);
    --vp-c-brand-3: rgba(90, 41, 228, .7);
}
/* 以前的vp-c-brand已弃用 */

:root {
    /* hero标题渐变色 */
    /*--vp-home-hero-name-color: transparent;*/
    /*--vp-home-hero-name-background: -webkit-linear-gradient(*/
    /*        120deg,*/
    /*        rgb(65,184,130),*/
    /*        var(--vp-c-brand-2)*/
    /*);*/

    /*hero logo背景渐变色 */
    --vp-home-hero-image-background-image: linear-gradient(
            -45deg,
            var(--vp-c-brand-3) 50%,
            var(--vp-c-brand-2) 50%
    );
    --vp-home-hero-image-filter: blur(30px);
}

/* 也可自行单独修改brand按钮 */
/* :root {
  --vp-button-brand-border: #F6CEEC;
  --vp-button-brand-text: #F6CEEC;
  --vp-button-brand-bg: #D939CD;

  --vp-button-brand-hover-border: #F6CEEC;
  --vp-button-brand-hover-text: #fff;
  --vp-button-brand-hover-bg: #D939CD;

  --vp-button-brand-active-border: #F6CEEC;
} */

/* 提示框背景颜色 */
:root {
    --vp-custom-block-tip-bg: var(--vp-c-green-soft);
}

/* 提示框 */
.custom-block.tip {
    border-color: var(--vp-c-green-2);
}

/* 警告框 */
.custom-block.warning {
    /* border-color: #d97706; */
    border-color: var(--vp-c-yellow-2);
}

/* 危险框 */
.custom-block.danger {
    /* border-color: #f43f5e; */
    border-color: var(--vp-c-red-2);
}

