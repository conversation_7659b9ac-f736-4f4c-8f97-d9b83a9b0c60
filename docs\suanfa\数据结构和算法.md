---
title: 数据结构和算法
description: >-
  # 数据结构和算法 ## 有向带权图的最算路径问题     有向带权图的单源最短路径问题：迪杰斯特拉算法 
  有向带权图的多源最短路径问题：佛洛依德算法      ## 单源最短路径问题  ### 概念
date: '2025-08-11T06:39:00.000Z'
updated: '2025-08-12T02:03:00.000Z'
category:
  - suanfa
  - suan1
  - suan2
tags:
  - AI
  - 前端
notion_id: 24c314c0-6846-8021-9e6d-ef33d9b8fd3e
notion_url: 'https://www.notion.so/24c314c0684680219e6def33d9b8fd3e'
---
# 数据结构和算法
## 有向带权图的最算路径问题 



有向带权图的单源最短路径问题：迪杰斯特拉算法

有向带权图的多源最短路径问题：佛洛依德算法





## 单源最短路径问题

### 概念

![](https://prod-files-secure.s3.us-west-2.amazonaws.com/6e61e255-cb1b-4012-9194-fa251b4c0f01/2b93b0df-54db-4086-b13c-f373aced360f/Untitled.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=ASIAZI2LB4665UQS73CL%2F20250812%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250812T060849Z&X-Amz-Expires=3600&X-Amz-Security-Token=IQoJb3JpZ2luX2VjEMX%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEaCXVzLXdlc3QtMiJIMEYCIQD7wa8364mVdZLWWdWL816A4tKWfoJHr88f4qrzoAJr%2BwIhAPgEZxy%2BGg1xB83trTQO3ziOkdjiKiGPmebtbU8KUxL5KogECP7%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEQABoMNjM3NDIzMTgzODA1IgxnJV98AwDLPlydCu8q3AOYxNxfjA16joZ2QM9cN31H5BosQIRXC2EMC92vLkl3GsyGnSiuawGsh9bAEZvUH6rVj%2Fg88cFmikojl4e1A3fvgjn07bmDQTs0iXglgYAhfcLPUmVMTg4QKGWtYYURwFC7kGX051vjvFvmuvay8fE4fhT3vLLAyYWub%2B4BYW7Jgiyq8FGsJ3P5ntdbB1Ue9kYHb2l7SxegqKcYhsAuF5uEpAP69phUjMqLOE1WhN2VY9KU7ucJpP8LvQbWwU6UX9SaOXA%2BKnsFgT64IJ9ENBWtnJfMm58AplX%2BKyJc%2B1fnA5dfPI%2F43UEKEakVz6KbJWzCAq3GFyOcbJx2h%2BBapbKK69PKdc8Pztun3XVKL5JjXJ%2Fu1UWK9pxhWaIcwJmEt0W0526L6X1QYC0yF2IPREzJRcy1LFaJTvT5BniirBTDaMn48qk5Cz2Jyn6IZcMLbo5VcqnOPtFmoGpG5AXUGgMhmlyFq%2FIBXHsmelAIAtNk8s40XhZ7Eg1Sds3q67XqAWZAA9U7DBYMLnRpxJ3kmhoqDsyKagZYbaXlCLWeQk9mLc7jGaXzb5SGcMbInTe3RIDMdcjo5jURpFcL%2BiBQyxUS7XyFo9gG5ygI8kZw0vEpHE9da4mx3XOyzxlBhDD0kOvEBjqkAQBYCJNJ73JEobEE57H6P1xvrBegpSy5a%2FbnWBLBt4FVhndi2IU6E%2FPxc3k5WE5tpnYTR%2Bk2MzxD2Wu6ajrQ0n%2BIDGSIl3oQykt2BB69kNDNtqhL6OGTTwhmhoxTvob%2F4HzhnnwQbo9ZAyBGAUVMaACWtgE4UbwStqibEJ8lTI%2BkHFOu9pBW%2FaZmgdfJl7oxTR147rpRdBYYPHzcv5OCwV24Texk&X-Amz-Signature=c6d28acbe00a4a3b9bb3344c23ae74a6edc928afdf9678948317f3433dc5985a&X-Amz-SignedHeaders=host&x-amz-checksum-mode=ENABLED&x-id=GetObject)

如果从A节点出发的话，计算出从A节点到达其他节点的最短路径，这就是单源最短路径问题。

### 迪杰斯特拉算法



在有向图中，从节点m到节点n之间具有边相连，但是并不代表从节点n出发返回节点m同样具有相连的边，既是具有相连的边，边的权值也有可能不同，所以，有向图的邻接矩阵表是一个不对称的矩阵 





**最短距离**：即从A点出发，直接连通或者经过其他节点到达这一节点的边权之和；

**最短路径**：记录一个字符串，用来表示从A点出发，直接连通或者经过其他节点到达这一节点的路径，字符串结尾一定是这个节点的取值。例如：B列最终的最短路径是ADGB，表示从A节点出发，途经D节点和G节点最终达到B节点，就是从A节点到B节点的最短路径；



