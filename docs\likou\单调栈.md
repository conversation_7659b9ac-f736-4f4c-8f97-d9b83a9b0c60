---
title: 单调栈
description: >-
  # 单调栈 做递增或者递减的题目  ## 适合类型   当前元素的左面和右面第一个比他大（小）的元素    ## 解释  栈
  保证栈顶到栈低是递增或者递减的顺序，这就是单调栈 （一般存下标）  单调栈
date: '2025-08-11T06:39:00.000Z'
updated: '2025-08-11T10:15:00.000Z'
category:
  - likou
tags:
  - 前端
notion_id: 24c314c0-6846-8084-b879-c0f501cd0942
notion_url: 'https://www.notion.so/24c314c068468084b879c0f501cd0942'
---
# 单调栈
做递增或者递减的题目

## 适合类型 

当前元素的左面和右面第一个比他大（小）的元素



## 解释

栈 保证栈顶到栈低是递增或者递减的顺序，这就是单调栈 （一般存下标）

单调栈的作用  存放遍历过的元素的下标

## 应用

[https://leetcode.cn/problems/daily-temperatures/description/](https://leetcode.cn/problems/daily-temperatures/description/)







