---
title: 数据结构和算法
description: >-
  # 数据结构和算法 ## 有向带权图的最算路径问题     有向带权图的单源最短路径问题：迪杰斯特拉算法 
  有向带权图的多源最短路径问题：佛洛依德算法      ## 单源最短路径问题  ### 概念
date: '2025-08-11T06:39:00.000Z'
updated: '2025-08-12T02:03:00.000Z'
category:
  - suanfa
  - suan1
  - suan2
tags:
  - AI
  - 前端
notion_id: 24c314c0-6846-8021-9e6d-ef33d9b8fd3e
notion_url: 'https://www.notion.so/24c314c0684680219e6def33d9b8fd3e'
---
# 数据结构和算法
## 有向带权图的最算路径问题 



有向带权图的单源最短路径问题：迪杰斯特拉算法

有向带权图的多源最短路径问题：佛洛依德算法





## 单源最短路径问题

### 概念

![](https://prod-files-secure.s3.us-west-2.amazonaws.com/6e61e255-cb1b-4012-9194-fa251b4c0f01/2b93b0df-54db-4086-b13c-f373aced360f/Untitled.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=ASIAZI2LB466YYJDVLAK%2F20250812%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250812T095226Z&X-Amz-Expires=3600&X-Amz-Security-Token=IQoJb3JpZ2luX2VjEMr%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEaCXVzLXdlc3QtMiJHMEUCIQDqxgcbJZ98dNcdwd4wyqFYbNwjSEiEXyOlvY3mEanguQIgGxdXDxEz181wrpeknSBkudXdmg%2Bb8kmFOsUHomUUYZ8q%2FwMIEhAAGgw2Mzc0MjMxODM4MDUiDMGSQQx9tHSGMCqcMircA6r14nseJdv0nrGf52qQkMMh0OWc9l6fZYKGp35P2n4GiIMNoQaG2XrH9StkM2TnhhplucR5KxGvzahk1W20ZWBrEKjgiX9%2B19%2BeUnqf9ynOvfrYUQMXfHxuP8o8qd5ZRSP7UfLsHjCA4h3YHv9i3TrysFuI1h95OfbKbDoDtthZ32B94B4zTyiBydnYYNXPf%2BW7cGoaRpvD2MNUXO0rMoVjRxG14DedopiW77guw0T%2FTEhz1WlXAZauTIzgepqLFglBeYYMWY9tMzvrDmMBL3S7ZM0NMSIHmw%2B4pUrTB9twQ%2FbQv7NHXP9%2Fmqj9rtBQTcKYetCguDMPFQ1c6nIAURbvA0G69g7sU3ro3WdF2BOTS0i6SnzwTgSQxdfDwlJGZcWgk9hWV25gmVhWba6kxwTM98PqFJxgCo87L2Kuh6Hr0cVepABwW9lYuc7aXy5BI5YHvSZ6eSUfVlFBsEmUSd3n1jiW%2Bv0Ht0nvodwXgjMQnYupYKgJ6hNMEu8X%2Fh0GV7TPSB5RfurxEzg9%2BdmTOJISp2DS7UaxgfHhRwpm1oVEjqd%2FLwWdDNF4wVO70L4Ccz5iOh0K2eVcd1602DRrGyGPkv0QMwvfJGEQYk5juaj6ITRXm%2BwJJNIEN41MMPiN7MQGOqUBIjRulpv0dXW0ddw1%2B7%2Fl9sTaI4N4Z3lAO4RTXk7W2CLtYCKsTOP%2FQczAdO53k%2FzuUo6vyCPQXvkf86WNNUMOEWsXPFvkmlRbiXzR%2FkYxAG%2Fvh%2BqrINROoWMkNbSNIPMYzeDD%2Bz3texUkReIq1myFIa7T%2BKfnTGXeJhhCarAISaL%2BnoQwlnS0PclEQOgEbB%2FbgjfXAp4ZH7DbsadptaIzm1Z5ATx%2B&X-Amz-Signature=9df8841b8b6650bd78f0d9805aa975da18cc3c48ef6220ee6804832022cef674&X-Amz-SignedHeaders=host&x-amz-checksum-mode=ENABLED&x-id=GetObject)

如果从A节点出发的话，计算出从A节点到达其他节点的最短路径，这就是单源最短路径问题。

### 迪杰斯特拉算法



在有向图中，从节点m到节点n之间具有边相连，但是并不代表从节点n出发返回节点m同样具有相连的边，既是具有相连的边，边的权值也有可能不同，所以，有向图的邻接矩阵表是一个不对称的矩阵 





**最短距离**：即从A点出发，直接连通或者经过其他节点到达这一节点的边权之和；

**最短路径**：记录一个字符串，用来表示从A点出发，直接连通或者经过其他节点到达这一节点的路径，字符串结尾一定是这个节点的取值。例如：B列最终的最短路径是ADGB，表示从A节点出发，途经D节点和G节点最终达到B节点，就是从A节点到B节点的最短路径；



