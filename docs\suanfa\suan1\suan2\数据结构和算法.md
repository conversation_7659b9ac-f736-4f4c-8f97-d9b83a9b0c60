---
title: 数据结构和算法
description: >-
  # 数据结构和算法 ## 有向带权图的最算路径问题     有向带权图的单源最短路径问题：迪杰斯特拉算法 
  有向带权图的多源最短路径问题：佛洛依德算法      ## 单源最短路径问题  ### 概念
date: '2025-08-11T06:39:00.000Z'
updated: '2025-08-12T02:03:00.000Z'
category:
  - suanfa
  - suan1
  - suan2
tags:
  - AI
  - 前端
notion_id: 24c314c0-6846-8021-9e6d-ef33d9b8fd3e
notion_url: 'https://www.notion.so/24c314c0684680219e6def33d9b8fd3e'
---
# 数据结构和算法
## 有向带权图的最算路径问题 



有向带权图的单源最短路径问题：迪杰斯特拉算法

有向带权图的多源最短路径问题：佛洛依德算法





## 单源最短路径问题

### 概念

![](https://img.winddrop.top/file/upload/1755227322176_notion_1755227314346_ledhir.png)

如果从A节点出发的话，计算出从A节点到达其他节点的最短路径，这就是单源最短路径问题。

### 迪杰斯特拉算法



在有向图中，从节点m到节点n之间具有边相连，但是并不代表从节点n出发返回节点m同样具有相连的边，既是具有相连的边，边的权值也有可能不同，所以，有向图的邻接矩阵表是一个不对称的矩阵 





**最短距离**：即从A点出发，直接连通或者经过其他节点到达这一节点的边权之和；

**最短路径**：记录一个字符串，用来表示从A点出发，直接连通或者经过其他节点到达这一节点的路径，字符串结尾一定是这个节点的取值。例如：B列最终的最短路径是ADGB，表示从A节点出发，途经D节点和G节点最终达到B节点，就是从A节点到B节点的最短路径；



