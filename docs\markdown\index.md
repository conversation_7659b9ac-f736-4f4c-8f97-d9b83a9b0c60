---
# https://vitepress.dev/reference/default-theme-home-page
layout: home
  
hero:
  name: "windDrop's blog"
  text: "学习笔记"
  image:
    src: /bg3.jpg
    alt: 背景图
  tagline: ''
  actions:
#    - theme: brand
#      text: 介绍
#      link: /markdown-examples
    - theme: alt
      text: View on GitHub
      link: https://github.com/raindropLiu

features:
  - title: React
    link: /react/reactBook   # 点击特性组件时链接。链接可以是内部的，也可以是外部的。
    icon:
        src: /react.svg
    details: react学习笔记
  - title: Vue
    icon:
        src: /vue.svg
    link: /vue/1
    details: 
  - title: Javascript
    icon:
      src: /js.svg
    details: 
  - title: 工具集合
    icon:
      src: /tool.svg
    details: 

---

